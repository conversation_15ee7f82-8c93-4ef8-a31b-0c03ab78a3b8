using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for VIX data retrieval, caching, and fallback mechanisms
/// </summary>
public class VixDataIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<ConnectionMultiplexer> _mockRedisConnection;
    private readonly Mock<IDatabase> _mockRedisDatabase;
    private readonly Mock<IVixCacheMetricsService> _mockMetricsService;
    private readonly VixFallbackService _vixService;
    private readonly VixCacheMetricsService _metricsService;

    public VixDataIntegrationTests(ITestOutputHelper output)
    {
        _output = output;
        
        // Setup mocks
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockRedisConnection = new Mock<ConnectionMultiplexer>();
        _mockRedisDatabase = new Mock<IDatabase>();
        _mockMetricsService = new Mock<IVixCacheMetricsService>();

        // Setup Redis mock
        _mockRedisConnection.Setup(x => x.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
            .Returns(_mockRedisDatabase.Object);

        // Setup HTTP client mock
        var httpClient = new HttpClient();
        _mockHttpClientFactory.Setup(x => x.CreateClient(It.IsAny<string>()))
            .Returns(httpClient);

        // Create logger
        var logger = new NullLogger<VixFallbackService>();
        var metricsLogger = new NullLogger<VixCacheMetricsService>();

        // Create services
        _metricsService = new VixCacheMetricsService(metricsLogger, _mockRedisConnection.Object);
        _vixService = new VixFallbackService(
            logger,
            _mockAlpacaFactory.Object,
            _mockHttpClientFactory.Object,
            _mockRedisConnection.Object,
            VixCacheConfig.Default,
            _metricsService);
    }

    [Fact]
    public async Task GetVixWithCachingAsync_WhenCacheEmpty_ShouldFetchFreshData()
    {
        // Arrange
        _mockRedisDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        _mockRedisDatabase.Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), 
            It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        // Act
        var result = await _vixService.GetVixWithCachingAsync();

        // Assert
        // Note: This test may return null if no external data sources are available
        // In a real integration test environment, you would have test data or mock external services
        _output.WriteLine($"VIX result: {result?.ToString() ?? "null"}");
        
        // Verify cache operations were attempted
        _mockRedisDatabase.Verify(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()), 
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task GetVixWithCachingAsync_WhenCacheHit_ShouldReturnCachedValue()
    {
        // Arrange
        var cachedVixData = new RedisVixData
        {
            Value = 18.5m,
            DataSource = "TestSource",
            RetrievedAt = DateTime.UtcNow.AddMinutes(-2),
            QualityScore = 0.9m,
            IsSynthetic = false,
            ApiCallsUsed = 1
        };

        _mockRedisDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(cachedVixData.ToJson());

        // Act
        var result = await _vixService.GetVixWithCachingAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(18.5m, result.Value);
        _output.WriteLine($"Cached VIX result: {result}");
    }

    [Fact]
    public async Task GetVixWithCachingAsync_WhenCacheExpired_ShouldFetchFreshData()
    {
        // Arrange
        var expiredVixData = new RedisVixData
        {
            Value = 18.5m,
            DataSource = "TestSource",
            RetrievedAt = DateTime.UtcNow.AddHours(-1), // Expired
            QualityScore = 0.9m,
            IsSynthetic = false,
            ApiCallsUsed = 1
        };

        _mockRedisDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(expiredVixData.ToJson());

        _mockRedisDatabase.Setup(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), 
            It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        // Act
        var result = await _vixService.GetVixWithCachingAsync();

        // Assert
        _output.WriteLine($"Fresh VIX result after cache expiry: {result?.ToString() ?? "null"}");
        
        // Verify fresh data fetch was attempted
        _mockRedisDatabase.Verify(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()), 
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task CalculateSyntheticVixAsync_ShouldReturnValidRange()
    {
        // Arrange
        var mockDataClient = new Mock<Alpaca.Markets.IAlpacaDataClient>();
        var mockBar = new Mock<Alpaca.Markets.IBar>();
        
        // Setup mock bar with reasonable VXX price
        mockBar.Setup(x => x.Close).Returns(25.50m);
        
        mockDataClient.Setup(x => x.GetLatestBarAsync(It.IsAny<Alpaca.Markets.LatestMarketDataRequest>(), 
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockBar.Object);

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        // Act
        var result = await _vixService.CalculateSyntheticVixAsync();

        // Assert
        Assert.NotNull(result);
        Assert.True(result >= 8m && result <= 80m, $"Synthetic VIX {result} should be between 8 and 80");
        _output.WriteLine($"Synthetic VIX result: {result}");
    }

    [Fact]
    public async Task GetVixFromWebAsync_ShouldHandleFailuresGracefully()
    {
        // Arrange
        var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(1); // Short timeout to force failures
        
        _mockHttpClientFactory.Setup(x => x.CreateClient(It.IsAny<string>()))
            .Returns(httpClient);

        var logger = new NullLogger<VixFallbackService>();
        var testService = new VixFallbackService(
            logger,
            _mockAlpacaFactory.Object,
            _mockHttpClientFactory.Object);

        // Act
        var result = await testService.GetVixFromWebAsync();

        // Assert
        // Should handle failures gracefully and return null
        _output.WriteLine($"Web scraping result (with forced failures): {result?.ToString() ?? "null"}");
        
        // The test passes if no exceptions are thrown
        Assert.True(true, "Web scraping handled failures gracefully");
    }

    [Fact]
    public async Task VixCacheMetrics_ShouldTrackPerformance()
    {
        // Arrange
        var metricsLogger = new NullLogger<VixCacheMetricsService>();
        var metricsService = new VixCacheMetricsService(metricsLogger, _mockRedisConnection.Object);

        // Act
        await metricsService.RecordCacheHitAsync("TestSource", TimeSpan.FromMinutes(2), 0.9m);
        await metricsService.RecordCacheMissAsync("Cache expired");
        await metricsService.RecordRetrievalPerformanceAsync("WebScraping", TimeSpan.FromSeconds(3), true, 2);

        var metrics = await metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(1, metrics.CacheHits);
        Assert.Equal(1, metrics.CacheMisses);
        Assert.Equal(1, metrics.TotalRetrievals);
        Assert.Equal(2, metrics.TotalApiCalls);
        Assert.Equal(0.5m, metrics.CacheHitRate);
        
        _output.WriteLine($"Cache metrics - Hits: {metrics.CacheHits}, Misses: {metrics.CacheMisses}, Hit Rate: {metrics.CacheHitRate:P}");
    }

    [Fact]
    public async Task VixCacheConfig_ShouldApplyCorrectTtl()
    {
        // Arrange
        var config = new VixCacheConfig
        {
            DefaultTtl = TimeSpan.FromMinutes(10),
            MinTtl = TimeSpan.FromMinutes(1),
            MaxTtl = TimeSpan.FromMinutes(30)
        };

        // Act & Assert
        Assert.Equal(TimeSpan.FromMinutes(10), config.DefaultTtl);
        Assert.Equal(TimeSpan.FromMinutes(1), config.MinTtl);
        Assert.Equal(TimeSpan.FromMinutes(30), config.MaxTtl);
        Assert.True(config.EnableCacheWarming);
        Assert.True(config.UseQualityBasedTtl);
        
        _output.WriteLine($"Cache config - Default TTL: {config.DefaultTtl}, Min: {config.MinTtl}, Max: {config.MaxTtl}");
    }

    [Fact]
    public async Task VixEtfModels_ShouldHaveValidCorrelations()
    {
        // This test validates the VIX ETF correlation models
        var models = new[]
        {
            new VixEtfModel("VXX", 0.92m, 0.85m, price => price * 1.45m + 8.2m, 1),
            new VixEtfModel("VIXY", 0.89m, 0.79m, price => price * 1.38m + 9.1m, 2),
            new VixEtfModel("VXZ", 0.85m, 0.72m, price => price * 1.25m + 7.8m, 3),
            new VixEtfModel("UVXY", 0.82m, 0.68m, price => price * 0.85m + 12.5m, 4),
            new VixEtfModel("SVXY", -0.87m, 0.75m, price => 52m - (price * 1.2m), 5)
        };

        foreach (var model in models)
        {
            // Test with reasonable ETF prices
            var testPrice = 25m;
            var calculatedVix = model.Calculator(testPrice);
            
            Assert.True(calculatedVix >= 8m && calculatedVix <= 80m, 
                $"VIX calculated from {model.Symbol} should be in valid range");
            Assert.True(Math.Abs(model.Correlation) <= 1m, 
                $"Correlation for {model.Symbol} should be between -1 and 1");
            Assert.True(model.RSquared >= 0m && model.RSquared <= 1m, 
                $"R-squared for {model.Symbol} should be between 0 and 1");
            
            _output.WriteLine($"{model.Symbol}: Price {testPrice} -> VIX {calculatedVix:F2} (Corr: {model.Correlation:F2}, R²: {model.RSquared:F2})");
        }
    }

    public void Dispose()
    {
        _vixService?.Dispose();
    }
}
